﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iad="clr-namespace:Infor.Activities.Debug;assembly=Infor.Activities.Debug"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:s="clr-namespace:System;assembly=mscorlib"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="vat" Type="InArgument(x:String)" />
    <x:Property Name="division" Type="InArgument(x:String)" />
    <x:Property Name="inbnValue" Type="InArgument(x:String)" />
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="logfile" Type="InArgument(x:String)" />
    <x:Property Name="CountryCode" Type="InArgument(x:String)" />
    <x:Property Name="VatPercentage" Type="InArgument(x:String)" />
    <x:Property Name="invoiceType" Type="InArgument(x:String)" />
    <x:Property Name="AccountStartsWith" Type="InArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.IONAPI</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
      <x:String>System</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System.Text.RegularExpressions</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.IONAPI</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence DisplayName="VatConfiguration_Sequence_MainSequence_1" sap2010:WorkflowViewState.IdRef="Sequence_2">
    <Sequence.Variables>
      <Variable x:TypeArguments="x:String" Name="vatCode" />
      <Variable x:TypeArguments="njl:JToken" Name="brDesOpt" />
      <Variable x:TypeArguments="x:String" Name="brFomat" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respOutput" />
      <Variable x:TypeArguments="x:Int32" Name="brRespStatus" />
      <Variable x:TypeArguments="x:Int32" Name="respout" />
      <Variable x:TypeArguments="njl:JToken" Name="out5" />
      <Variable x:TypeArguments="x:String" Name="vatAmt" />
      <Variable x:TypeArguments="x:Int32" Name="totalVats" />
      <Variable x:TypeArguments="x:Int32" Default="0" Name="Itr" />
      <Variable x:TypeArguments="x:String" Name="percentage" />
      <Variable x:TypeArguments="x:String" Name="per" />
      <Variable x:TypeArguments="iru:ResponseObject" Name="respout1" />
    </Sequence.Variables>
    <Assign DisplayName="VatConfiguration_Assign_AccountStartsWith_2" sap2010:WorkflowViewState.IdRef="Assign_24">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[AccountStartsWith]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[If(invoiceType = "po","",AccountStartsWith)]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign DisplayName="VatConfiguration_Assign_percentage_3" sap2010:WorkflowViewState.IdRef="Assign_18">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[percentage]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">[VatPercentage]</InArgument>
      </Assign.Value>
    </Assign>
    <TryCatch DisplayName="VatConfiguration_TryCatch_totalVats_4" sap2010:WorkflowViewState.IdRef="TryCatch_5">
      <TryCatch.Try>
        <Assign DisplayName="VatConfiguration_Assign_totalVats_5" sap2010:WorkflowViewState.IdRef="Assign_15">
          <Assign.To>
            <OutArgument x:TypeArguments="x:Int32">[totalVats]</OutArgument>
          </Assign.To>
          <Assign.Value>
            <InArgument x:TypeArguments="x:Int32">[vat.split(","c).length]</InArgument>
          </Assign.Value>
        </Assign>
      </TryCatch.Try>
      <TryCatch.Catches>
        <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_5">
          <ActivityAction x:TypeArguments="s:Exception">
            <ActivityAction.Argument>
              <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
            </ActivityAction.Argument>
            <Assign DisplayName="VatConfiguration_Assign_totalVatsException_6" sap2010:WorkflowViewState.IdRef="Assign_32">
              <Assign.To>
                <OutArgument x:TypeArguments="x:Int32">[totalVats]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:Int32">[1]</InArgument>
              </Assign.Value>
            </Assign>
          </ActivityAction>
        </Catch>
      </TryCatch.Catches>
    </TryCatch>
    <If Condition="[totalVats&gt;1]" DisplayName="VatConfiguration_If_totalVats_7" sap2010:WorkflowViewState.IdRef="If_13">
      <If.Then>
        <While DisplayName="VatConfiguration_While_Itr_8" sap2010:WorkflowViewState.IdRef="While_1" Condition="[Itr &lt; totalVats]">
          <Sequence DisplayName="VatConfiguration_Sequence_WhileSequence_9" sap2010:WorkflowViewState.IdRef="Sequence_8">
            <Assign DisplayName="VatConfiguration_Assign_vatAmt_10" sap2010:WorkflowViewState.IdRef="Assign_16">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[vatAmt]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[vat.split(","c)(Itr)]</InArgument>
              </Assign.Value>
            </Assign>
            <Assign DisplayName="VatConfiguration_Assign_per_11" sap2010:WorkflowViewState.IdRef="Assign_19">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[per]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[percentage.split(","c)(Itr).Replace("%","")]</InArgument>
              </Assign.Value>
            </Assign>
            <TryCatch DisplayName="VatConfiguration_TryCatch_VatPercentage_12" sap2010:WorkflowViewState.IdRef="TryCatch_2">
              <TryCatch.Try>
                <Assign DisplayName="VatConfiguration_Assign_VatPercentageConvert_13" sap2010:WorkflowViewState.IdRef="Assign_5">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[VatPercentage]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[Convert.toDouble(Regex.Replace(per, "%", "").Trim()).tostring]</InArgument>
                  </Assign.Value>
                </Assign>
              </TryCatch.Try>
              <TryCatch.Catches>
                <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_2">
                  <ActivityAction x:TypeArguments="s:Exception">
                    <ActivityAction.Argument>
                      <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                    </ActivityAction.Argument>
                    <Assign DisplayName="VatConfiguration_Assign_VatPercentageZero_14" sap2010:WorkflowViewState.IdRef="Assign_25">
                      <Assign.To>
                        <OutArgument x:TypeArguments="x:String">[VatPercentage]</OutArgument>
                      </Assign.To>
                      <Assign.Value>
                        <InArgument x:TypeArguments="x:String">0</InArgument>
                      </Assign.Value>
                    </Assign>
                  </ActivityAction>
                </Catch>
              </TryCatch.Catches>
            </TryCatch>
            <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="VatConfiguration_TemplateApply_brfomat_15" sap2010:WorkflowViewState.IdRef="Template_Apply_5" Template="{}{ 'parameters': {  'AccountStartsWith':'{{%AccountStartsWith%}}','VatPercentage': '{{%VatPercentage%}}' ,'CountryCode': '{{%CountryCode%}}','invoiceType': '{{%invoiceType%}}' } }" Text="[brfomat]">
              <ias:Template_Apply.Values>
                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                  <scg:List x:TypeArguments="x:String" Capacity="4">
                    <x:String>VatPercentage</x:String>
                    <x:String>CountryCode</x:String>
                    <x:String>invoiceType</x:String>
                    <x:String>AccountStartsWith</x:String>
                  </scg:List>
                  <scg:List x:TypeArguments="x:String" Capacity="4">
                    <x:String>VatPercentage</x:String>
                    <x:String>CountryCode</x:String>
                    <x:String>invoiceType</x:String>
                    <x:String>AccountStartsWith</x:String>
                  </scg:List>
                </scg:List>
              </ias:Template_Apply.Values>
            </ias:Template_Apply>
            <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="VatConfiguration_DeserializeJSON_brDesOpt_16" sap2010:WorkflowViewState.IdRef="DeserializeJSON_4" JTokenObject="[brDesOpt]" JTokenString="[brFomat]" />
            <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" Title="{x:Null}" ContinueOnError="True" DisplayName="VatConfiguration_MessageBox_brDesOpt_17" sap2010:WorkflowViewState.IdRef="MessageBox_2" Selection="OK" Text="[brDesOpt.tostring]" />
            <iad:CommentOut DisplayName="VatConfiguration_CommentOut_Empty_18" sap2010:WorkflowViewState.IdRef="CommentOut_3">
              <iad:CommentOut.Activities>
                <sco:Collection x:TypeArguments="Activity" />
              </iad:CommentOut.Activities>
            </iad:CommentOut>
            <iai:IONAPIRequestWizard FileAttachments="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="VatConfiguration_IONAPIRequestWizard_respOutput_19" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_5" PostData="[brDesOpt.toString()]" Response="[respOutput]" StatusCode="[brRespStatus]" Url="[tenantID+&quot;IONSERVICES/businessrules/decision/execute/&quot;+&quot;VatCodeConfiguration&quot;]">
              <iai:IONAPIRequestWizard.Headers>
                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                  <scg:List x:TypeArguments="x:String" Capacity="0" />
                  <scg:List x:TypeArguments="x:String" Capacity="0" />
                </scg:List>
              </iai:IONAPIRequestWizard.Headers>
              <iai:IONAPIRequestWizard.QueryParameters>
                <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                  <scg:List x:TypeArguments="x:String" Capacity="4">
                    <x:String>matrixName</x:String>
                  </scg:List>
                  <scg:List x:TypeArguments="x:String" Capacity="4">
                    <x:String>VatCodeConfiguration</x:String>
                  </scg:List>
                </scg:List>
              </iai:IONAPIRequestWizard.QueryParameters>
            </iai:IONAPIRequestWizard>
            <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" Title="{x:Null}" ContinueOnError="True" DisplayName="VatConfiguration_MessageBox_respOutput_20" sap2010:WorkflowViewState.IdRef="MessageBox_4" Selection="OK" Text="[respOutput.ReadAsText]" />
            <iad:CommentOut DisplayName="VatConfiguration_CommentOut_Empty2_21" sap2010:WorkflowViewState.IdRef="CommentOut_4">
              <iad:CommentOut.Activities>
                <sco:Collection x:TypeArguments="Activity" />
              </iad:CommentOut.Activities>
            </iad:CommentOut>
            <Assign DisplayName="VatConfiguration_Assign_vatCode_22" sap2010:WorkflowViewState.IdRef="Assign_21">
              <Assign.To>
                <OutArgument x:TypeArguments="x:String">[vatCode]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:String">[JToken.Parse(respOutput.ReadAsText)("parameters")("VatCode").ToString]</InArgument>
              </Assign.Value>
            </Assign>
            <If Condition="[vatCode = &quot;NA&quot;]" DisplayName="VatConfiguration_If_vatCodeNA_23" sap2010:WorkflowViewState.IdRef="If_12">
              <If.Then>
                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="VatConfiguration_IONAPIRequestWizard_respout1_24" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_7" Response="[respout1]" ResponseCode="[respout]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                  <iai:IONAPIRequestWizard.Headers>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>Accept</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>application/json</x:String>
                      </scg:List>
                    </scg:List>
                  </iai:IONAPIRequestWizard.Headers>
                  <iai:IONAPIRequestWizard.QueryParameters>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>INBN</x:String>
                        <x:String>RDTP</x:String>
                        <x:String>DIVI</x:String>
                        <x:String>GLAM</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>inbnValue</x:String>
                        <x:String>3</x:String>
                        <x:String>division</x:String>
                        <x:String>vatAmt</x:String>
                      </scg:List>
                    </scg:List>
                  </iai:IONAPIRequestWizard.QueryParameters>
                </iai:IONAPIRequestWizard>
              </If.Then>
              <If.Else>
                <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="VatConfiguration_IONAPIRequestWizard_respout1Else_25" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_8" Response="[respout1]" ResponseCode="[respout]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                  <iai:IONAPIRequestWizard.Headers>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>Accept</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="4">
                        <x:String>application/json</x:String>
                      </scg:List>
                    </scg:List>
                  </iai:IONAPIRequestWizard.Headers>
                  <iai:IONAPIRequestWizard.QueryParameters>
                    <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                      <scg:List x:TypeArguments="x:String" Capacity="8">
                        <x:String>INBN</x:String>
                        <x:String>RDTP</x:String>
                        <x:String>DIVI</x:String>
                        <x:String>VTA1</x:String>
                        <x:String>VTCD</x:String>
                      </scg:List>
                      <scg:List x:TypeArguments="x:String" Capacity="8">
                        <x:String>inbnValue</x:String>
                        <x:String>3</x:String>
                        <x:String>division</x:String>
                        <x:String>vatAmt</x:String>
                        <x:String>vatCode</x:String>
                      </scg:List>
                    </scg:List>
                  </iai:IONAPIRequestWizard.QueryParameters>
                </iai:IONAPIRequestWizard>
              </If.Else>
            </If>
            <Assign DisplayName="VatConfiguration_Assign_out5_26" sap2010:WorkflowViewState.IdRef="Assign_12">
              <Assign.To>
                <OutArgument x:TypeArguments="njl:JToken">[out5]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respout1.ReadAsText)]</InArgument>
              </Assign.Value>
            </Assign>
            <TryCatch DisplayName="VatConfiguration_TryCatch_ResponseCheck_27" sap2010:WorkflowViewState.IdRef="TryCatch_1">
              <TryCatch.Try>
                <If Condition="[respout = 200]" DisplayName="VatConfiguration_If_respout200_28" sap2010:WorkflowViewState.IdRef="If_7">
                  <If.Then>
                    <If Condition="[out5(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" DisplayName="VatConfiguration_If_out5Records_29" sap2010:WorkflowViewState.IdRef="If_6">
                      <If.Then>
                        <Sequence DisplayName="VatConfiguration_Sequence_ErrorSequence_30" sap2010:WorkflowViewState.IdRef="Sequence_6">
                          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="VatConfiguration_AppendLine_errorMessage_31" sap2010:WorkflowViewState.IdRef="Append_Line_5" Line="[(out5(&quot;results&quot;)(0)(&quot;errorMessage&quot;)).ToString]" Source="[logfile]" />
                        </Sequence>
                      </If.Then>
                      <If.Else>
                        <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="VatConfiguration_AppendLine_VatLineAdded_32" sap2010:WorkflowViewState.IdRef="Append_Line_6" Line="Vat line added" Source="[logfile]" />
                      </If.Else>
                    </If>
                  </If.Then>
                  <If.Else>
                    <Sequence DisplayName="VatConfiguration_Sequence_ErrorSequence2_33" sap2010:WorkflowViewState.IdRef="Sequence_7">
                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="VatConfiguration_AppendLine_ErrorAddingVat_34" sap2010:WorkflowViewState.IdRef="Append_Line_7" Line="Error while adding the Vat line" Source="[logfile]" />
                    </Sequence>
                  </If.Else>
                </If>
              </TryCatch.Try>
              <TryCatch.Catches>
                <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_1">
                  <ActivityAction x:TypeArguments="s:Exception">
                    <ActivityAction.Argument>
                      <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                    </ActivityAction.Argument>
                    <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="VatConfiguration_AppendLine_ExceptionVat_35" sap2010:WorkflowViewState.IdRef="Append_Line_4" Line="Exception while adding Vat line" Source="[logfile]" />
                  </ActivityAction>
                </Catch>
              </TryCatch.Catches>
            </TryCatch>
            <Assign DisplayName="VatConfiguration_Assign_ItrIncrement_36" sap2010:WorkflowViewState.IdRef="Assign_17">
              <Assign.To>
                <OutArgument x:TypeArguments="x:Int32">[Itr]</OutArgument>
              </Assign.To>
              <Assign.Value>
                <InArgument x:TypeArguments="x:Int32">[Itr+1]</InArgument>
              </Assign.Value>
            </Assign>
          </Sequence>
        </While>
      </If.Then>
      <If.Else>
        <Sequence DisplayName="VatConfiguration_Sequence_ElseSequence_37" sap2010:WorkflowViewState.IdRef="Sequence_11">
          <TryCatch DisplayName="VatConfiguration_TryCatch_VatPercentageElse_38" sap2010:WorkflowViewState.IdRef="TryCatch_6">
            <TryCatch.Try>
              <Assign DisplayName="VatConfiguration_Assign_VatPercentageConvertElse_39" sap2010:WorkflowViewState.IdRef="Assign_35">
                <Assign.To>
                  <OutArgument x:TypeArguments="x:String">[VatPercentage]</OutArgument>
                </Assign.To>
                <Assign.Value>
                  <InArgument x:TypeArguments="x:String">[Convert.toDouble(Regex.Replace(VatPercentage, "%", "").Trim()).tostring]</InArgument>
                </Assign.Value>
              </Assign>
            </TryCatch.Try>
            <TryCatch.Catches>
              <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_6">
                <ActivityAction x:TypeArguments="s:Exception">
                  <ActivityAction.Argument>
                    <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                  </ActivityAction.Argument>
                  <Assign DisplayName="VatConfiguration_Assign_VatPercentageZeroElse_40" sap2010:WorkflowViewState.IdRef="Assign_36">
                    <Assign.To>
                      <OutArgument x:TypeArguments="x:String">[VatPercentage]</OutArgument>
                    </Assign.To>
                    <Assign.Value>
                      <InArgument x:TypeArguments="x:String">0</InArgument>
                    </Assign.Value>
                  </Assign>
                </ActivityAction>
              </Catch>
            </TryCatch.Catches>
          </TryCatch>
          <Assign DisplayName="VatConfiguration_Assign_vatAmtElse_41" sap2010:WorkflowViewState.IdRef="Assign_26">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[vatAmt]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[vat]</InArgument>
            </Assign.Value>
          </Assign>
          <ias:Template_Apply ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="VatConfiguration_TemplateApply_brfomatElse_42" sap2010:WorkflowViewState.IdRef="Template_Apply_6" Template="{}{ 'parameters': {  'AccountStartsWith':'{{%AccountStartsWith%}}','VatPercentage': '{{%VatPercentage%}}' ,'CountryCode': '{{%CountryCode%}}','invoiceType': '{{%invoiceType%}}' } }" Text="[brfomat]">
            <ias:Template_Apply.Values>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>VatPercentage</x:String>
                  <x:String>CountryCode</x:String>
                  <x:String>invoiceType</x:String>
                  <x:String>AccountStartsWith</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>VatPercentage</x:String>
                  <x:String>CountryCode</x:String>
                  <x:String>invoiceType</x:String>
                  <x:String>AccountStartsWith</x:String>
                </scg:List>
              </scg:List>
            </ias:Template_Apply.Values>
          </ias:Template_Apply>
          <ias:DeserializeJSON ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="VatConfiguration_DeserializeJSON_brDesOptElse_43" sap2010:WorkflowViewState.IdRef="DeserializeJSON_5" JTokenObject="[brDesOpt]" JTokenString="[brFomat]" />
          <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" Title="{x:Null}" ContinueOnError="True" DisplayName="VatConfiguration_MessageBox_brDesOptElse_44" sap2010:WorkflowViewState.IdRef="MessageBox_1" Selection="OK" Text="[brDesOpt.tostring]" />
          <iad:CommentOut DisplayName="VatConfiguration_CommentOut_EmptyElse_45" sap2010:WorkflowViewState.IdRef="CommentOut_1">
            <iad:CommentOut.Activities>
              <sco:Collection x:TypeArguments="Activity" />
            </iad:CommentOut.Activities>
          </iad:CommentOut>
          <iai:IONAPIRequestWizard FileAttachments="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="VatConfiguration_IONAPIRequestWizard_respOutputElse_46" HttpMethod="POST" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_9" PostData="[brDesOpt.toString()]" Response="[respOutput]" StatusCode="[brRespStatus]" Url="[tenantID+&quot;IONSERVICES/businessrules/decision/execute/&quot;+&quot;VatCodeConfiguration&quot;]">
            <iai:IONAPIRequestWizard.Headers>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="0" />
                <scg:List x:TypeArguments="x:String" Capacity="0" />
              </scg:List>
            </iai:IONAPIRequestWizard.Headers>
            <iai:IONAPIRequestWizard.QueryParameters>
              <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>matrixName</x:String>
                </scg:List>
                <scg:List x:TypeArguments="x:String" Capacity="4">
                  <x:String>VatCodeConfiguration</x:String>
                </scg:List>
              </scg:List>
            </iai:IONAPIRequestWizard.QueryParameters>
          </iai:IONAPIRequestWizard>
          <ias:MessageBox ButtonSelection="{x:Null}" ErrorCode="{x:Null}" Title="{x:Null}" ContinueOnError="True" DisplayName="VatConfiguration_MessageBox_respOutputElse_47" sap2010:WorkflowViewState.IdRef="MessageBox_3" Selection="OK" Text="[respOutput.ReadAsText]" />
          <iad:CommentOut DisplayName="VatConfiguration_CommentOut_Empty2Else_48" sap2010:WorkflowViewState.IdRef="CommentOut_2">
            <iad:CommentOut.Activities>
              <sco:Collection x:TypeArguments="Activity" />
            </iad:CommentOut.Activities>
          </iad:CommentOut>
          <Assign DisplayName="VatConfiguration_Assign_vatCodeElse_49" sap2010:WorkflowViewState.IdRef="Assign_30">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[vatCode]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">[JToken.Parse(respOutput.ReadAsText)("parameters")("VatCode").ToString]</InArgument>
            </Assign.Value>
          </Assign>
          <If Condition="[vatCode = &quot;NA&quot;]" DisplayName="VatConfiguration_If_vatCodeNAElse_50" sap2010:WorkflowViewState.IdRef="If_14">
            <If.Then>
              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="VatConfiguration_IONAPIRequestWizard_respout1ElseThen_51" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_10" Response="[respout1]" ResponseCode="[respout]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                <iai:IONAPIRequestWizard.Headers>
                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>Accept</x:String>
                    </scg:List>
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>application/json</x:String>
                    </scg:List>
                  </scg:List>
                </iai:IONAPIRequestWizard.Headers>
                <iai:IONAPIRequestWizard.QueryParameters>
                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>INBN</x:String>
                      <x:String>RDTP</x:String>
                      <x:String>DIVI</x:String>
                      <x:String>GLAM</x:String>
                    </scg:List>
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>inbnValue</x:String>
                      <x:String>3</x:String>
                      <x:String>division</x:String>
                      <x:String>vatAmt</x:String>
                    </scg:List>
                  </scg:List>
                </iai:IONAPIRequestWizard.QueryParameters>
              </iai:IONAPIRequestWizard>
            </If.Then>
            <If.Else>
              <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" StatusCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="VatConfiguration_IONAPIRequestWizard_respout1ElseElse_52" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_11" Response="[respout1]" ResponseCode="[respout]" Url="[TenantID+ &quot;M3/m3api-rest/v2/execute/APS450MI/AddLine&quot;]">
                <iai:IONAPIRequestWizard.Headers>
                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>Accept</x:String>
                    </scg:List>
                    <scg:List x:TypeArguments="x:String" Capacity="4">
                      <x:String>application/json</x:String>
                    </scg:List>
                  </scg:List>
                </iai:IONAPIRequestWizard.Headers>
                <iai:IONAPIRequestWizard.QueryParameters>
                  <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
                    <scg:List x:TypeArguments="x:String" Capacity="8">
                      <x:String>INBN</x:String>
                      <x:String>RDTP</x:String>
                      <x:String>DIVI</x:String>
                      <x:String>VTA1</x:String>
                      <x:String>VTCD</x:String>
                    </scg:List>
                    <scg:List x:TypeArguments="x:String" Capacity="8">
                      <x:String>inbnValue</x:String>
                      <x:String>3</x:String>
                      <x:String>division</x:String>
                      <x:String>vatAmt</x:String>
                      <x:String>vatCode</x:String>
                    </scg:List>
                  </scg:List>
                </iai:IONAPIRequestWizard.QueryParameters>
              </iai:IONAPIRequestWizard>
            </If.Else>
          </If>
          <Assign DisplayName="VatConfiguration_Assign_out5Else_53" sap2010:WorkflowViewState.IdRef="Assign_31">
            <Assign.To>
              <OutArgument x:TypeArguments="njl:JToken">[out5]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respout1.ReadAsText)]</InArgument>
            </Assign.Value>
          </Assign>
          <TryCatch DisplayName="VatConfiguration_TryCatch_ResponseCheckElse_54" sap2010:WorkflowViewState.IdRef="TryCatch_4">
            <TryCatch.Try>
              <If Condition="[respout = 200]" DisplayName="VatConfiguration_If_respout200Else_55" sap2010:WorkflowViewState.IdRef="If_16">
                <If.Then>
                  <If Condition="[out5(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" DisplayName="VatConfiguration_If_out5RecordsElse_56" sap2010:WorkflowViewState.IdRef="If_15">
                    <If.Then>
                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="VatConfiguration_AppendLine_errorMessageElse_57" sap2010:WorkflowViewState.IdRef="Append_Line_12" Line="[(out5(&quot;results&quot;)(0)(&quot;errorMessage&quot;)).ToString]" Source="[logfile]" />
                    </If.Then>
                    <If.Else>
                      <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="VatConfiguration_AppendLine_VatLineAddedElse_58" sap2010:WorkflowViewState.IdRef="Append_Line_9" Line="Vat line added" Source="[logfile]" />
                    </If.Else>
                  </If>
                </If.Then>
                <If.Else>
                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="VatConfiguration_AppendLine_ErrorAddingVatElse_59" sap2010:WorkflowViewState.IdRef="Append_Line_13" Line="Error while adding the Vat line" Source="[logfile]" />
                </If.Else>
              </If>
            </TryCatch.Try>
            <TryCatch.Catches>
              <Catch x:TypeArguments="s:Exception" sap2010:WorkflowViewState.IdRef="Catch`1_4">
                <ActivityAction x:TypeArguments="s:Exception">
                  <ActivityAction.Argument>
                    <DelegateInArgument x:TypeArguments="s:Exception" Name="exception" />
                  </ActivityAction.Argument>
                  <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="VatConfiguration_AppendLine_ExceptionVatElse_60" sap2010:WorkflowViewState.IdRef="Append_Line_11" Line="Exception while adding Vat line" Source="[logfile]" />
                </ActivityAction>
              </Catch>
            </TryCatch.Catches>
          </TryCatch>
        </Sequence>
      </If.Else>
    </If>
    <sads:DebugSymbol.Symbol>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</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_24" sap:VirtualizedContainerService.HintSize="1269.33333333333,62" />
      <sap2010:ViewStateData Id="Assign_18" sap:VirtualizedContainerService.HintSize="1269.33333333333,62" />
      <sap2010:ViewStateData Id="Assign_15" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_32" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_5" sap:VirtualizedContainerService.HintSize="404.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_5" sap:VirtualizedContainerService.HintSize="1269.33333333333,300">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_16" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_19" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Assign_5" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_25" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_2" sap:VirtualizedContainerService.HintSize="404.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_2" sap:VirtualizedContainerService.HintSize="464,300">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Template_Apply_5" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="DeserializeJSON_4" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="MessageBox_2" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="CommentOut_3" sap:VirtualizedContainerService.HintSize="464,66" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_5" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="MessageBox_4" sap:VirtualizedContainerService.HintSize="464,22" />
      <sap2010:ViewStateData Id="CommentOut_4" sap:VirtualizedContainerService.HintSize="464,66" />
      <sap2010:ViewStateData Id="Assign_21" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_7" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_8" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_12" sap:VirtualizedContainerService.HintSize="464,214">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_12" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Append_Line_5" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_6" sap:VirtualizedContainerService.HintSize="222,146">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_6" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_6" sap:VirtualizedContainerService.HintSize="464,294" />
      <sap2010:ViewStateData Id="Append_Line_7" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Sequence_7" sap:VirtualizedContainerService.HintSize="222,146">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_7" sap:VirtualizedContainerService.HintSize="711,442">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_4" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Catch`1_1" sap:VirtualizedContainerService.HintSize="716.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_1" sap:VirtualizedContainerService.HintSize="464,52.6666666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_17" sap:VirtualizedContainerService.HintSize="464,62" />
      <sap2010:ViewStateData Id="Sequence_8" sap:VirtualizedContainerService.HintSize="486,1802.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="While_1" sap:VirtualizedContainerService.HintSize="512.666666666667,1966.66666666667">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_35" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Assign_36" sap:VirtualizedContainerService.HintSize="242,62" />
      <sap2010:ViewStateData Id="Catch`1_6" sap:VirtualizedContainerService.HintSize="404.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_6" sap:VirtualizedContainerService.HintSize="708.666666666667,300">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_26" sap:VirtualizedContainerService.HintSize="708.666666666667,62" />
      <sap2010:ViewStateData Id="Template_Apply_6" sap:VirtualizedContainerService.HintSize="708.666666666667,22" />
      <sap2010:ViewStateData Id="DeserializeJSON_5" sap:VirtualizedContainerService.HintSize="708.666666666667,22" />
      <sap2010:ViewStateData Id="MessageBox_1" sap:VirtualizedContainerService.HintSize="708.666666666667,22" />
      <sap2010:ViewStateData Id="CommentOut_1" sap:VirtualizedContainerService.HintSize="708.666666666667,66" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_9" sap:VirtualizedContainerService.HintSize="708.666666666667,22" />
      <sap2010:ViewStateData Id="MessageBox_3" sap:VirtualizedContainerService.HintSize="708.666666666667,22" />
      <sap2010:ViewStateData Id="CommentOut_2" sap:VirtualizedContainerService.HintSize="708.666666666667,66" />
      <sap2010:ViewStateData Id="Assign_30" sap:VirtualizedContainerService.HintSize="708.666666666667,62" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_10" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_11" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_14" sap:VirtualizedContainerService.HintSize="708.666666666667,214">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_31" sap:VirtualizedContainerService.HintSize="708.666666666667,62" />
      <sap2010:ViewStateData Id="Append_Line_12" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Append_Line_9" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_15" sap:VirtualizedContainerService.HintSize="464,214" />
      <sap2010:ViewStateData Id="Append_Line_13" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="If_16" sap:VirtualizedContainerService.HintSize="690,368">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_11" sap:VirtualizedContainerService.HintSize="200,22" />
      <sap2010:ViewStateData Id="Catch`1_4" sap:VirtualizedContainerService.HintSize="694.666666666667,22">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">False</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="TryCatch_4" sap:VirtualizedContainerService.HintSize="708.666666666667,606">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Sequence_11" sap:VirtualizedContainerService.HintSize="730.666666666667,2152">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_13" sap:VirtualizedContainerService.HintSize="1269.33333333333,2306" />
      <sap2010:ViewStateData Id="Sequence_2" sap:VirtualizedContainerService.HintSize="1291.33333333333,2974">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="1331.33333333333,3214" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>