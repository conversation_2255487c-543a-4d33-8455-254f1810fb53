﻿<Activity mc:Ignorable="sap sap2010 sads" x:Class="RehostedWorkflowDesigner.Workflow"
 xmlns="http://schemas.microsoft.com/netfx/2009/xaml/activities"
 xmlns:iai="clr-namespace:Infor.Activities.IONAPI;assembly=Infor.Activities.IONAPI"
 xmlns:ias="clr-namespace:Infor.Activities.Sys;assembly=Infor.Activities.Sys"
 xmlns:iru="clr-namespace:Infor.RPA.Utilities;assembly=Infor.RPA.Utilities"
 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
 xmlns:mva="clr-namespace:Microsoft.VisualBasic.Activities;assembly=System.Activities"
 xmlns:njl="clr-namespace:Newtonsoft.Json.Linq;assembly=Newtonsoft.Json"
 xmlns:sads="http://schemas.microsoft.com/netfx/2010/xaml/activities/debugger"
 xmlns:sap="http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation"
 xmlns:sap2010="http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation"
 xmlns:scg="clr-namespace:System.Collections.Generic;assembly=mscorlib"
 xmlns:sco="clr-namespace:System.Collections.ObjectModel;assembly=mscorlib"
 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
  <x:Members>
    <x:Property Name="tenantID" Type="InArgument(x:String)" />
    <x:Property Name="logfile" Type="InArgument(x:String)" />
    <x:Property Name="vendorId" Type="OutArgument(x:String)" />
    <x:Property Name="company" Type="OutArgument(x:String)" />
    <x:Property Name="division" Type="OutArgument(x:String)" />
    <x:Property Name="result" Type="OutArgument(scg:List(x:String))" />
    <x:Property Name="Status" Type="OutArgument(x:String)" />
    <x:Property Name="commentStatus" Type="OutArgument(x:String)" />
    <x:Property Name="pono" Type="InArgument(x:String)" />
  </x:Members>
  <mva:VisualBasic.Settings>
    <x:Null />
  </mva:VisualBasic.Settings>
  <sap2010:WorkflowViewState.IdRef>RehostedWorkflowDesigner.Workflow_1</sap2010:WorkflowViewState.IdRef>
  <TextExpression.NamespacesForImplementation>
    <sco:Collection x:TypeArguments="x:String">
      <x:String>System.Activities</x:String>
      <x:String>System.Activities.Statements</x:String>
      <x:String>System.Activities.Expressions</x:String>
      <x:String>System.Activities.Validation</x:String>
      <x:String>System.Activities.XamlIntegration</x:String>
      <x:String>System.Windows.Controls</x:String>
      <x:String>System.Windows.Documents</x:String>
      <x:String>System.Windows.Shapes</x:String>
      <x:String>System.Windows.Shell</x:String>
      <x:String>System.Windows.Navigation</x:String>
      <x:String>System.Windows.Data</x:String>
      <x:String>System.Windows</x:String>
      <x:String>System.Windows.Controls.Primitives</x:String>
      <x:String>System.Windows.Media.Animation</x:String>
      <x:String>System.Windows.Input</x:String>
      <x:String>System.Windows.Media</x:String>
      <x:String>System.Diagnostics</x:String>
      <x:String>System.Windows.Automation</x:String>
      <x:String>System.Windows.Media.TextFormatting</x:String>
      <x:String>System.Windows.Ink</x:String>
      <x:String>System.Windows.Media.Effects</x:String>
      <x:String>System.Windows.Media.Imaging</x:String>
      <x:String>System.Windows.Media.Media3D</x:String>
      <x:String>System.Windows.Controls.Ribbon</x:String>
      <x:String>System.Windows.Forms.Integration</x:String>
      <x:String>Infor.Activities.Email</x:String>
      <x:String>Infor.Activities.IONAPI</x:String>
      <x:String>Infor.Activities.Sys</x:String>
      <x:String>Infor.RPA.Utilities</x:String>
      <x:String>Microsoft.VisualBasic.Activities</x:String>
      <x:String>Microsoft.VisualBasic.CompilerServices</x:String>
      <x:String>Newtonsoft.Json</x:String>
      <x:String>Newtonsoft.Json.Linq</x:String>
      <x:String>System</x:String>
      <x:String>System.Activities.Debugger.Symbol</x:String>
      <x:String>System.Collections.Generic</x:String>
      <x:String>System.Windows.Markup</x:String>
    </sco:Collection>
  </TextExpression.NamespacesForImplementation>
  <TextExpression.ReferencesForImplementation>
    <sco:Collection x:TypeArguments="AssemblyReference">
      <AssemblyReference>System.Activities</AssemblyReference>
      <AssemblyReference>PresentationFramework</AssemblyReference>
      <AssemblyReference>WindowsBase</AssemblyReference>
      <AssemblyReference>PresentationCore</AssemblyReference>
      <AssemblyReference>System.Windows.Controls.Ribbon</AssemblyReference>
      <AssemblyReference>WindowsFormsIntegration</AssemblyReference>
      <AssemblyReference>Infor.Activities.Email</AssemblyReference>
      <AssemblyReference>Infor.Activities.IONAPI</AssemblyReference>
      <AssemblyReference>Infor.Activities.Sys</AssemblyReference>
      <AssemblyReference>Infor.RPA.Utilities</AssemblyReference>
      <AssemblyReference>Microsoft.VisualBasic</AssemblyReference>
      <AssemblyReference>Newtonsoft.Json</AssemblyReference>
      <AssemblyReference>mscorlib</AssemblyReference>
      <AssemblyReference>System.Runtime.WindowsRuntime</AssemblyReference>
      <AssemblyReference>System.ServiceModel</AssemblyReference>
      <AssemblyReference>System</AssemblyReference>
      <AssemblyReference>System.Core</AssemblyReference>
      <AssemblyReference>Microsoft.Bcl.AsyncInterfaces</AssemblyReference>
      <AssemblyReference>System.Xaml</AssemblyReference>
      <AssemblyReference>System.ComponentModel.Composition</AssemblyReference>
      <AssemblyReference>System.Memory</AssemblyReference>
    </sco:Collection>
  </TextExpression.ReferencesForImplementation>
  <Sequence DisplayName="ExportMI_MainSequence_1" sap2010:WorkflowViewState.IdRef="Sequence_3">
    <Sequence.Variables>
      <Variable x:TypeArguments="iru:ResponseObject" Name="respObj1" />
      <Variable x:TypeArguments="x:Int32" Name="StatusCode1" />
      <Variable x:TypeArguments="x:String" Name="req1" />
      <Variable x:TypeArguments="x:String" Name="cucd" />
      <Variable x:TypeArguments="x:String" Name="pyme" />
      <Variable x:TypeArguments="x:String" Name="tepy" />
    </Sequence.Variables>
    <Assign DisplayName="ExportMI_Assign_result_2" sap2010:WorkflowViewState.IdRef="Assign_12">
      <Assign.To>
        <OutArgument x:TypeArguments="scg:List(x:String)">[result]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="scg:List(x:String)">[New List(Of String)()]</InArgument>
      </Assign.Value>
    </Assign>
    <Assign DisplayName="ExportMI_Assign_req1_3" sap2010:WorkflowViewState.IdRef="Assign_2">
      <Assign.To>
        <OutArgument x:TypeArguments="x:String">[req1]</OutArgument>
      </Assign.To>
      <Assign.Value>
        <InArgument x:TypeArguments="x:String">["IACONO,IADIVI,IASUNO,IACUCD,IATEPY,IAPYME from MPHEAD where IAPUNO = '" + pono + "'"]</InArgument>
      </Assign.Value>
    </Assign>
    <iai:IONAPIRequestWizard FileAttachments="{x:Null}" PostData="{x:Null}" ResponseCode="{x:Null}" ContentType="application_json" ContinueOnError="True" DisplayName="ExportMI_IONAPIRequestWizard_respObj1_4" HttpMethod="GET" sap2010:WorkflowViewState.IdRef="IONAPIRequestWizard_1" Response="[respObj1]" StatusCode="[StatusCode1]" Url="[TenantID + &quot;M3/m3api-rest/v2/execute/EXPORTMI/Select?SEPC=~&amp;HDRS=0&amp;dateformat=YMD8&amp;excludeempty=false&amp;righttrim=true&amp;format=PRETTY&amp;extendedresult=false&quot;]">
      <iai:IONAPIRequestWizard.Headers>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>Accept</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>application/json</x:String>
          </scg:List>
        </scg:List>
      </iai:IONAPIRequestWizard.Headers>
      <iai:IONAPIRequestWizard.QueryParameters>
        <scg:List x:TypeArguments="scg:List(x:String)" Capacity="4">
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>QERY</x:String>
            <x:String>SEPC</x:String>
          </scg:List>
          <scg:List x:TypeArguments="x:String" Capacity="4">
            <x:String>req1</x:String>
            <x:String>~</x:String>
          </scg:List>
        </scg:List>
      </iai:IONAPIRequestWizard.QueryParameters>
    </iai:IONAPIRequestWizard>
    <If Condition="[StatusCode1=200]" DisplayName="ExportMI_If_StatusCode1_5" sap2010:WorkflowViewState.IdRef="If_4">
      <If.Then>
        <Sequence DisplayName="ExportMI_Sequence_SuccessSequence_6" sap2010:WorkflowViewState.IdRef="Sequence_2">
          <Sequence.Variables>
            <Variable x:TypeArguments="njl:JToken" Name="out1" />
          </Sequence.Variables>
          <Assign DisplayName="ExportMI_Assign_out1_7" sap2010:WorkflowViewState.IdRef="Assign_3">
            <Assign.To>
              <OutArgument x:TypeArguments="njl:JToken">[out1]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="njl:JToken">[JToken.Parse(respObj1.ReadAsText)]</InArgument>
            </Assign.Value>
          </Assign>
          <If Condition="[out1(&quot;results&quot;)(0)(&quot;records&quot;).ToString = &quot;[]&quot;]" DisplayName="ExportMI_If_out1_8" sap2010:WorkflowViewState.IdRef="If_3">
            <If.Then>
              <Sequence DisplayName="ExportMI_Sequence_NoRecordsSequence_9" sap2010:WorkflowViewState.IdRef="Sequence_5">
                <Assign DisplayName="ExportMI_Assign_Status_10" sap2010:WorkflowViewState.IdRef="Assign_15">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">NEEDSVERIFICATION</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign DisplayName="ExportMI_Assign_commentStatus_11" sap2010:WorkflowViewState.IdRef="Assign_16">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">["PO number is not available"]</InArgument>
                  </Assign.Value>
                </Assign>
                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="ExportMI_AppendLine_commentStatus_12" sap2010:WorkflowViewState.IdRef="Append_Line_4" Line="[commentStatus]" Source="[logFile]" />
              </Sequence>
            </If.Then>
            <If.Else>
              <Sequence DisplayName="ExportMI_Sequence_ProcessRecordsSequence_13" sap2010:WorkflowViewState.IdRef="Sequence_1">
                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="ExportMI_AppendLine_logfile_14" sap2010:WorkflowViewState.IdRef="Append_Line_1" Line="Extracted the company and division." Source="[logfile]" />
                <Assign DisplayName="ExportMI_Assign_division_15" sap2010:WorkflowViewState.IdRef="Assign_17">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[division]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[out1("results")(0)("records")(0)("REPL").ToString().split("~"C)(1).ToString()]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign DisplayName="ExportMI_Assign_company_16" sap2010:WorkflowViewState.IdRef="Assign_5">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[company]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[out1("results")(0)("records")(0)("REPL").ToString().split("~"C)(0).ToString()]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign DisplayName="ExportMI_Assign_vendorId_17" sap2010:WorkflowViewState.IdRef="Assign_6">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[vendorId]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[out1("results")(0)("records")(0)("REPL").ToString().split("~"C)(2).ToString()]</InArgument>
                  </Assign.Value>
                </Assign>
                <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="ExportMI_AppendLine_vendorid_18" sap2010:WorkflowViewState.IdRef="Append_Line_2" Line="[&quot;VendorID : &quot; + vendorid]" Source="[logFile]" />
                <Assign DisplayName="ExportMI_Assign_tepy_19" sap2010:WorkflowViewState.IdRef="Assign_9">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[tepy]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[out1("results")(0)("records")(0)("REPL").ToString().split("~"C)(4).ToString()]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign DisplayName="ExportMI_Assign_pyme_20" sap2010:WorkflowViewState.IdRef="Assign_10">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[pyme]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[out1("results")(0)("records")(0)("REPL").ToString().split("~"C)(5).ToString()]</InArgument>
                  </Assign.Value>
                </Assign>
                <Assign DisplayName="ExportMI_Assign_cucd_21" sap2010:WorkflowViewState.IdRef="Assign_11">
                  <Assign.To>
                    <OutArgument x:TypeArguments="x:String">[cucd]</OutArgument>
                  </Assign.To>
                  <Assign.Value>
                    <InArgument x:TypeArguments="x:String">[out1("results")(0)("records")(0)("REPL").ToString().split("~"C)(3).ToString()]</InArgument>
                  </Assign.Value>
                </Assign>
                <InvokeMethod DisplayName="ExportMI_InvokeMethod_company_22" sap2010:WorkflowViewState.IdRef="InvokeMethod_2" MethodName="Add">
                  <InvokeMethod.TargetObject>
                    <InArgument x:TypeArguments="scg:List(x:String)">[result]</InArgument>
                  </InvokeMethod.TargetObject>
                  <InArgument x:TypeArguments="x:String">[company]</InArgument>
                </InvokeMethod>
                <InvokeMethod DisplayName="ExportMI_InvokeMethod_division_23" sap2010:WorkflowViewState.IdRef="InvokeMethod_3" MethodName="Add">
                  <InvokeMethod.TargetObject>
                    <InArgument x:TypeArguments="scg:List(x:String)">[result]</InArgument>
                  </InvokeMethod.TargetObject>
                  <InArgument x:TypeArguments="x:String">[division]</InArgument>
                </InvokeMethod>
                <InvokeMethod DisplayName="ExportMI_InvokeMethod_vendorId_24" sap2010:WorkflowViewState.IdRef="InvokeMethod_4" MethodName="Add">
                  <InvokeMethod.TargetObject>
                    <InArgument x:TypeArguments="scg:List(x:String)">[result]</InArgument>
                  </InvokeMethod.TargetObject>
                  <InArgument x:TypeArguments="x:String">[vendorId]</InArgument>
                </InvokeMethod>
                <InvokeMethod DisplayName="ExportMI_InvokeMethod_cucd_25" sap2010:WorkflowViewState.IdRef="InvokeMethod_5" MethodName="Add">
                  <InvokeMethod.TargetObject>
                    <InArgument x:TypeArguments="scg:List(x:String)">[result]</InArgument>
                  </InvokeMethod.TargetObject>
                  <InArgument x:TypeArguments="x:String">[cucd]</InArgument>
                </InvokeMethod>
                <InvokeMethod DisplayName="ExportMI_InvokeMethod_tepy_26" sap2010:WorkflowViewState.IdRef="InvokeMethod_6" MethodName="Add">
                  <InvokeMethod.TargetObject>
                    <InArgument x:TypeArguments="scg:List(x:String)">[result]</InArgument>
                  </InvokeMethod.TargetObject>
                  <InArgument x:TypeArguments="x:String">[tepy]</InArgument>
                </InvokeMethod>
                <InvokeMethod DisplayName="ExportMI_InvokeMethod_pyme_27" sap2010:WorkflowViewState.IdRef="InvokeMethod_7" MethodName="Add">
                  <InvokeMethod.TargetObject>
                    <InArgument x:TypeArguments="scg:List(x:String)">[result]</InArgument>
                  </InvokeMethod.TargetObject>
                  <InArgument x:TypeArguments="x:String">[pyme]</InArgument>
                </InvokeMethod>
              </Sequence>
            </If.Else>
          </If>
        </Sequence>
      </If.Then>
      <If.Else>
        <Sequence DisplayName="ExportMI_Sequence_ErrorSequence_28" sap2010:WorkflowViewState.IdRef="Sequence_4">
          <Assign DisplayName="ExportMI_Assign_commentStatus_29" sap2010:WorkflowViewState.IdRef="Assign_13">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[commentStatus]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">["Received an IONAPI error while fetching company number and division for the PO " + pono +"."]</InArgument>
            </Assign.Value>
          </Assign>
          <Assign DisplayName="ExportMI_Assign_Status_30" sap2010:WorkflowViewState.IdRef="Assign_14">
            <Assign.To>
              <OutArgument x:TypeArguments="x:String">[Status]</OutArgument>
            </Assign.To>
            <Assign.Value>
              <InArgument x:TypeArguments="x:String">["FAILURE"]</InArgument>
            </Assign.Value>
          </Assign>
          <ias:Append_Line ErrorCode="{x:Null}" ContinueOnError="True" DisplayName="ExportMI_AppendLine_commentStatus_31" sap2010:WorkflowViewState.IdRef="Append_Line_3" Line="[commentStatus]" Source="[logfile]" />
        </Sequence>
      </If.Else>
    </If>
    <sads:DebugSymbol.Symbol>d1RDOlxVc2Vyc1xkbWl0dGFwYWxsaVxBcHBEYXRhXExvY2FsXEluZm9yUlBBXE0zSW52b2ljZVByb2Nlc3NpbmdHZW5BSVYzXEV4cG9ydE1JLnhhbWxSYAOpAg4CAQFpBXAOAgF1cQV4DgIBcHkFkAEfAgFpkQEFpwIKAgECbjpuUQIBeGs7a0MCAXZ2MHaHAQIBc3MxczcCAXF5owJ5rwICAW55zwJ5jQQCAWx5uwJ5ygICAWqRARORASYCAQOTAQmQAhQCARSTAgmlAhQCAQWXAQueARQCAWWfAQuPAhACARWUAgubAhQCAQ+cAgujAhQCAQukAgukAtcBAgEGnAE4nAFbAgFomQE5mQE/AgFmnwEZnwFoAgEWoQEPswEaAgFXtgEPjQIaAgEXmQI2mQKVAQIBEpYCN5YCRgIBEKECNqECQQIBDp4CN54CPwIBDKQCsAGkAsEBAgEJpALJAaQC1AECAQeiARGpARoCAWGqARGxARoCAV2yARGyAd0BAgFYtwERtwHrAQIBU7gBEb8BGgIBT8ABEccBGgIBS8gBEc8BGgIBR9ABEdAB7QECAULRARHYARoCAT7ZARHgARoCATrhARHoARoCATbpARHuASACATHvARH0ASACASz1ARH6ASACASf7ARGAAiACASKBAhGGAiACAR2HAhGMAiACARinATynAU0CAWSkAT2kAUUCAWKvATyvAVoCAWCsAT2sAUwCAV6yAbYBsgHHAQIBW7IBzwGyAdoBAgFZtwGwAbcB1QECAVa3Ad0BtwHoAQIBVL0BPL0BiwECAVK6AT26AUcCAVDFATzFAYsBAgFOwgE9wgFGAgFMzQE8zQGLAQIBSsoBPcoBRwIBSNABsQHQAdcBAgFF0AHfAdAB6gECAUPWATzWAYsBAgFB0wE90wFDAgE/3gE83gGLAQIBPdsBPdsBQwIBO+YBPOYBiwECATnjAT3jAUMCATfrAUbrAU4CATTtATrtAUMCATLxAUbxAU4CAS/zATrzAUQCAS33AUb3AU4CASr5ATr5AUQCASj9AUb9AU4CASX/ATr/AUACASODAkaDAk4CASCFAjqFAkACAR6JAkaJAk4CARuLAjqLAkACARk=</sads:DebugSymbol.Symbol>
  </Sequence>
  <sap2010:WorkflowViewState.ViewStateManager>
    <sap2010:ViewStateManager>
      <sap2010:ViewStateData Id="Assign_12" sap:VirtualizedContainerService.HintSize="864,60" />
      <sap2010:ViewStateData Id="Assign_2" sap:VirtualizedContainerService.HintSize="864,60" />
      <sap2010:ViewStateData Id="IONAPIRequestWizard_1" sap:VirtualizedContainerService.HintSize="864,22" />
      <sap2010:ViewStateData Id="Assign_3" sap:VirtualizedContainerService.HintSize="553,60" />
      <sap2010:ViewStateData Id="Assign_15" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_16" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_4" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_5" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Append_Line_1" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_17" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_5" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_6" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_2" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Assign_9" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_10" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_11" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="InvokeMethod_2" sap:VirtualizedContainerService.HintSize="242,128" />
      <sap2010:ViewStateData Id="InvokeMethod_3" sap:VirtualizedContainerService.HintSize="242,128" />
      <sap2010:ViewStateData Id="InvokeMethod_4" sap:VirtualizedContainerService.HintSize="242,128" />
      <sap2010:ViewStateData Id="InvokeMethod_5" sap:VirtualizedContainerService.HintSize="242,128" />
      <sap2010:ViewStateData Id="InvokeMethod_6" sap:VirtualizedContainerService.HintSize="242,128" />
      <sap2010:ViewStateData Id="InvokeMethod_7" sap:VirtualizedContainerService.HintSize="242,128" />
      <sap2010:ViewStateData Id="Sequence_1" sap:VirtualizedContainerService.HintSize="264,1816">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_3" sap:VirtualizedContainerService.HintSize="553,1964" />
      <sap2010:ViewStateData Id="Sequence_2" sap:VirtualizedContainerService.HintSize="575,2188">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
            <x:Boolean x:Key="IsPinned">False</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="Assign_13" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Assign_14" sap:VirtualizedContainerService.HintSize="242,60" />
      <sap2010:ViewStateData Id="Append_Line_3" sap:VirtualizedContainerService.HintSize="242,22" />
      <sap2010:ViewStateData Id="Sequence_4" sap:VirtualizedContainerService.HintSize="264,346">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="If_4" sap:VirtualizedContainerService.HintSize="864,2336" />
      <sap2010:ViewStateData Id="Sequence_3" sap:VirtualizedContainerService.HintSize="886,2722">
        <sap:WorkflowViewStateService.ViewState>
          <scg:Dictionary x:TypeArguments="x:String, x:Object">
            <x:Boolean x:Key="IsExpanded">True</x:Boolean>
          </scg:Dictionary>
        </sap:WorkflowViewStateService.ViewState>
      </sap2010:ViewStateData>
      <sap2010:ViewStateData Id="RehostedWorkflowDesigner.Workflow_1" sap:VirtualizedContainerService.HintSize="926,2922" />
    </sap2010:ViewStateManager>
  </sap2010:WorkflowViewState.ViewStateManager>
</Activity>